//
//  VideoPlayerService.swift
//  cop
//
//  Created by AI Assistant on 2025/6/1.
//  轻量级视频播放服务 - 最简化实现
//

import SwiftUI
import AVFoundation
import AVKit
import Foundation

// MARK: - 视频播放错误类型
enum VideoPlayerError: LocalizedError {
    case fileNotFound
    case unsupportedFormat
    case loadingFailed

    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "视频文件未找到"
        case .unsupportedFormat:
            return "不支持的视频格式"
        case .loadingFailed:
            return "视频加载失败"
        }
    }
}

// MARK: - 播放器状态
enum VideoPlayerState: Equatable {
    case idle
    case loading
    case ready
    case playing
    case paused
    case error(VideoPlayerError)

    var isPlaying: Bool {
        if case .playing = self { return true }
        return false
    }
}

// MARK: - 播放器模式
enum VideoPlayerMode {
    case preview
    case fullScreen
}

// MARK: - 简化的视频播放器
@MainActor
class VideoPlayer: ObservableObject {

    // MARK: - 唯一标识
    let id = UUID()

    // MARK: - Published Properties
    @Published var state: VideoPlayerState = .idle
    @Published var currentTime: TimeInterval = 0
    @Published var duration: TimeInterval = 0

    // MARK: - Private Properties
    private var player: AVPlayer?
    private var timeObserver: Any?
    private var notificationObservers: [NSObjectProtocol] = []

    // MARK: - 初始化
    init(mode: VideoPlayerMode = .fullScreen) {
        print("🎬 VideoPlayer \(id) 初始化")
    }

    deinit {
        print("🎬 VideoPlayer \(id) 开始释放")
        // 在deinit中同步清理资源
        syncRemoveObservers()
        player?.pause()
        player?.replaceCurrentItem(with: nil)
        print("🎬 VideoPlayer \(id) 已释放")
    }

    // MARK: - 公开方法

    /// 加载视频
    func loadVideo(from mediaFile: MediaFileInfo) {
        cleanup()

        guard FileManager.default.fileExists(atPath: mediaFile.localURL.path) else {
            state = .error(.fileNotFound)
            return
        }

        state = .loading

        let avPlayer = AVPlayer(url: mediaFile.localURL)
        self.player = avPlayer

        setupObservers()

        // 获取视频时长（使用新的API）
        if let playerItem = avPlayer.currentItem {
            Task {
                do {
                    let duration = try await playerItem.asset.load(.duration)
                    if duration.isValid && !duration.isIndefinite {
                        await MainActor.run {
                            self.duration = duration.seconds
                        }
                    }
                } catch {
                    print("🎬 获取视频时长失败: \(error)")
                }
            }
        }

        state = .ready
        print("🎬 VideoPlayer \(id) 视频加载完成: \(mediaFile.name)")
    }

    /// 获取播放器实例
    func getPlayer() -> AVPlayer? {
        return player
    }

    /// 清理资源
    func cleanup() {
        removeObservers()
        player?.pause()
        player?.replaceCurrentItem(with: nil)
        player = nil
        state = .idle
        currentTime = 0
        duration = 0
    }

    // MARK: - 私有方法

    /// 设置观察者
    private func setupObservers() {
        guard let player = player else { return }

        // 时间观察者
        let interval = CMTime(seconds: 0.5, preferredTimescale: 1000)
        timeObserver = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            guard let self = self else { return }
            Task { @MainActor in
                self.currentTime = time.seconds
            }
        }

        // 播放结束通知
        if let playerItem = player.currentItem {
            let endObserver = NotificationCenter.default.addObserver(
                forName: .AVPlayerItemDidPlayToEndTime,
                object: playerItem,
                queue: .main
            ) { [weak self] _ in
                guard let self = self else { return }
                Task { @MainActor in
                    self.state = .ready
                }
            }
            notificationObservers.append(endObserver)
        }
    }

    /// 移除观察者（主线程版本）
    private func removeObservers() {
        if let timeObserver = timeObserver, let player = player {
            player.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }

        notificationObservers.forEach { NotificationCenter.default.removeObserver($0) }
        notificationObservers.removeAll()
    }

    /// 同步移除观察者（用于deinit）
    private nonisolated func syncRemoveObservers() {
        // 在deinit中，我们不能访问主线程隔离的属性
        // 所以我们简化处理，让系统自动清理
        print("🎬 VideoPlayer deinit - 观察者将由系统自动清理")
    }
}

// MARK: - 向后兼容性别名
typealias VideoPlayerService = VideoPlayer