//
//  MediaContentView.swift
//  cop
//
//  Created by 阿亮 on 2025/6/1.
//  轻量级媒体内容视图 - 使用原生组件
//

import SwiftUI
import AVKit

// MARK: - 轻量级媒体内容视图
struct MediaContentView: View {
    let mediaFile: MediaFileInfo
    @Binding var uiVisibility: UIVisibilityState
    let screenSize: CGSize

    // 文件切换功能的参数
    @Binding var currentIndex: Int
    let totalCount: Int
    let onDismiss: () -> Void
    let onToggleUI: () -> Void

    var body: some View {
        Group {
            if mediaFile.type == .image {
                ImageContentView(
                    mediaFile: mediaFile,
                    uiVisibility: $uiVisibility,
                    screenSize: screenSize,
                    currentIndex: $currentIndex,
                    totalCount: totalCount,
                    onDismiss: onDismiss,
                    onToggleUI: onToggleUI
                )
            } else if mediaFile.type == .video {
                // 使用iOS原生VideoPlayer，最简化实现
                NativeVideoPlayerView(
                    mediaFile: mediaFile,
                    onDismiss: onDismiss
                )
                .ignoresSafeArea(.all)
            } else {
                // 未知文件类型的占位视图
                UnsupportedMediaView(mediaFile: mediaFile, onDismiss: onDismiss)
            }
        }
    }
}

// MARK: - 原生视频播放器视图
struct NativeVideoPlayerView: View {
    let mediaFile: MediaFileInfo
    let onDismiss: () -> Void

    @State private var player: AVPlayer?
    @State private var isLoading = true
    @State private var hasError = false

    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()

            if isLoading {
                ProgressView("正在加载视频...")
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .foregroundColor(.white)
            } else if hasError {
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 64))
                        .foregroundColor(.orange)

                    Text("视频加载失败")
                        .font(.title2)
                        .foregroundColor(.white)

                    Button("返回") {
                        onDismiss()
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(10)
                }
            } else if let player = player {
                AVKit.VideoPlayer(player: player)
                    .ignoresSafeArea()
                    .gesture(
                        DragGesture()
                            .onEnded { value in
                                // 向下滑动退出
                                if value.translation.height > 100 {
                                    onDismiss()
                                }
                            }
                    )
            }
        }
        .onAppear {
            loadVideo()
        }
        .onDisappear {
            player?.pause()
            player = nil
        }
    }

    private func loadVideo() {
        guard FileManager.default.fileExists(atPath: mediaFile.localURL.path) else {
            hasError = true
            isLoading = false
            return
        }

        let avPlayer = AVPlayer(url: mediaFile.localURL)
        self.player = avPlayer
        self.isLoading = false

        print("🎬 原生VideoPlayer加载: \(mediaFile.name)")
    }
}

// MARK: - 不支持的媒体类型视图
struct UnsupportedMediaView: View {
    let mediaFile: MediaFileInfo
    let onDismiss: () -> Void

    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()

            VStack(spacing: 24) {
                Image(systemName: "questionmark.circle.fill")
                    .font(.system(size: 64))
                    .foregroundColor(.orange)

                Text("不支持的文件类型")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Text(mediaFile.name)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                Button("返回") {
                    onDismiss()
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(10)
            }
        }
    }
}
