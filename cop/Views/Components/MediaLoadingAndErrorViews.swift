//
//  MediaLoadingAndErrorViews.swift
//  cop
//
//  Created by 阿亮 on 2025/6/1.
//

import SwiftUI

// MARK: - 加载视图
struct MediaLoadingView: View {
    let message: String
    
    init(message: String = "正在加载...") {
        self.message = message
    }
    
    var body: some View {
        VStack(spacing: 24) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.3)

            Text(message)
                .foregroundColor(.white.opacity(0.8))
                .font(.system(size: 18, weight: .medium))
        }
    }
}

// MARK: - 图片加载视图
struct ImageLoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)

            Text("加载中...")
                .foregroundColor(.white.opacity(0.8))
                .font(.system(size: 17, weight: .medium))
        }
    }
}

// MARK: - 视频加载视图
struct VideoLoadingView: View {
    var body: some View {
        VStack(spacing: 24) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.3)

            Text("正在加载视频...")
                .foregroundColor(.white.opacity(0.8))
                .font(.system(size: 18, weight: .medium))
        }
    }
}

// MARK: - 媒体错误视图
struct MediaErrorView: View {
    let errorType: VideoPlayerError
    let mediaFile: MediaFileInfo
    let onRetry: () -> Void

    var body: some View {
        VStack(spacing: 24) {
            // 错误图标
            ErrorIcon(errorType: errorType)

            // 错误信息
            ErrorMessage(errorType: errorType)

            // 文件信息
            FileInfo(mediaFile: mediaFile)

            // 重试按钮
            RetryButton(onRetry: onRetry)
        }
        .padding(.horizontal, 32)
    }
}

// MARK: - 图片错误视图
struct ImageErrorView: View {
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo")
                .font(.system(size: 48))
                .foregroundColor(.white.opacity(0.6))

            Text("无法加载图片")
                .foregroundColor(.white.opacity(0.8))
                .font(.system(size: 17, weight: .medium))

            Button("重新加载") {
                onRetry()
            }
            .foregroundColor(.white)
            .font(.system(size: 16, weight: .medium))
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(.blue.opacity(0.8), in: Capsule())
        }
    }
}

// MARK: - 错误图标
private struct ErrorIcon: View {
    let errorType: VideoPlayerError
    
    var body: some View {
        Image(systemName: iconName)
            .font(.system(size: 56))
            .foregroundColor(.white.opacity(0.6))
    }
    
    private var iconName: String {
        switch errorType {
        case .fileNotFound:
            return "doc.questionmark"
        case .unsupportedFormat:
            return "video.slash"
        case .loadingFailed:
            return "exclamationmark.triangle"
        }
    }
}

// MARK: - 错误消息
private struct ErrorMessage: View {
    let errorType: VideoPlayerError
    
    var body: some View {
        VStack(spacing: 12) {
            Text(errorType.localizedDescription)
                .foregroundColor(.white.opacity(0.9))
                .font(.system(size: 20, weight: .semibold))

            Text(recoverySuggestion)
                .foregroundColor(.white.opacity(0.7))
                .font(.system(size: 16))
                .multilineTextAlignment(.center)
                .lineLimit(3)
        }
    }
    
    private var recoverySuggestion: String {
        switch errorType {
        case .fileNotFound:
            return "请检查文件是否存在"
        case .unsupportedFormat:
            return "该视频格式不受支持"
        case .loadingFailed:
            return "视频加载失败，请重试"
        }
    }
}

// MARK: - 文件信息
private struct FileInfo: View {
    let mediaFile: MediaFileInfo
    
    var body: some View {
        VStack(spacing: 8) {
            Text(mediaFile.name)
                .foregroundColor(.white.opacity(0.6))
                .font(.system(size: 14, weight: .medium))
                .lineLimit(1)

            Text("格式: \(mediaFile.localURL.pathExtension.uppercased())")
                .foregroundColor(.white.opacity(0.5))
                .font(.system(size: 12))
        }
    }
}

// MARK: - 重试按钮
private struct RetryButton: View {
    let onRetry: () -> Void
    
    var body: some View {
        Button(action: onRetry) {
            HStack(spacing: 8) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 14, weight: .medium))

                Text("重新加载")
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(.blue.opacity(0.8), in: Capsule())
        }
        .buttonStyle(PlainButtonStyle())
    }
}
