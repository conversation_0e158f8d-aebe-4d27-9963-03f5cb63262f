//
//  VideoPlayerView.swift
//  cop
//
//  Created by AI Assistant on 2025/6/1.
//  轻量级视频播放器视图 - 使用iOS原生组件
//

import SwiftUI
import AVFoundation
import AVKit

// MARK: - 轻量级视频播放器视图
struct VideoPlayerView: View {
    let mediaFile: MediaFileInfo
    let mode: VideoPlayerMode

    // 回调
    let onDismiss: (() -> Void)?
    let onError: ((VideoPlayerError) -> Void)?

    // 状态管理 - 使用轻量级播放器
    @StateObject private var videoPlayer: VideoPlayer

    init(
        mediaFile: MediaFileInfo,
        mode: VideoPlayerMode = .fullScreen,
        onDismiss: (() -> Void)? = nil,
        onError: ((VideoPlayerError) -> Void)? = nil
    ) {
        self.mediaFile = mediaFile
        self.mode = mode
        self.onDismiss = onDismiss
        self.onError = onError

        // 为每个视图创建独立的播放器实例
        self._videoPlayer = StateObject(wrappedValue: VideoPlayer(mode: mode))
    }

    var body: some View {
        ZStack {
            // 背景
            Color.black
                .ignoresSafeArea(.all)

            // 主要内容
            mainContent

            // 全屏模式的交互层
            if mode == .fullScreen {
                fullScreenInteractionLayer
            }
        }
        .onAppear {
            loadVideo()
        }
        .onDisappear {
            // 清理播放器资源
            videoPlayer.cleanup()
        }
        .onChange(of: videoPlayer.state) { _, newState in
            handleStateChange(newState)
        }
    }

    // MARK: - 主要内容
    @ViewBuilder
    private var mainContent: some View {
        switch videoPlayer.state {
        case .idle:
            EmptyView()

        case .loading:
            loadingView

        case .ready, .playing, .paused:
            if let avPlayer = videoPlayer.getPlayer() {
                playerView(avPlayer)
            }

        case .error(let error):
            errorView(error)
        }
    }

    // MARK: - 加载视图
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))

            if mode == .fullScreen {
                Text("正在加载视频...")
                    .foregroundColor(.white)
                    .font(.headline)
            }
        }
    }

    // MARK: - 播放器视图
    private func playerView(_ avPlayer: AVPlayer) -> some View {
        // 使用iOS原生VideoPlayer，简化实现
        AVKit.VideoPlayer(player: avPlayer)
            .ignoresSafeArea(.all)
            .onAppear {
                print("🎬 VideoPlayer视图出现，播放器状态: \(videoPlayer.state)")
                // 确保播放器准备就绪后开始播放
                if videoPlayer.state == .ready {
                    avPlayer.play()
                }
            }
            .onDisappear {
                print("🎬 VideoPlayer视图消失，停止播放")
                avPlayer.pause()
            }
    }

    // MARK: - 错误视图
    private func errorView(_ error: VideoPlayerError) -> some View {
        VStack(spacing: 24) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 64))
                .foregroundColor(.orange)

            Text("播放出现问题")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            Text(error.localizedDescription)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            HStack(spacing: 20) {
                Button("重试") {
                    loadVideo()
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(10)

                Button("返回") {
                    onDismiss?()
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.gray)
                .cornerRadius(10)
            }
            .padding(.top)
        }
        .padding()
    }


    // MARK: - 全屏交互层
    private var fullScreenInteractionLayer: some View {
        ZStack {
            // 下滑关闭手势区域
            swipeToCloseGestureArea

            // 退出按钮和下滑提示
            if onDismiss != nil {
                VStack {
                    HStack {
                        // 下滑提示指示器
                        swipeIndicator
                        Spacer()
                        // 退出按钮
                        exitButton
                    }
                    Spacer()
                }
            }
        }
    }

    // MARK: - 下滑提示指示器
    private var swipeIndicator: some View {
        VStack(spacing: 4) {
            // 小横条指示器
            RoundedRectangle(cornerRadius: 2)
                .fill(.white.opacity(0.6))
                .frame(width: 36, height: 4)

            // 提示文字
            Text("下滑退出")
                .font(.caption2)
                .foregroundColor(.white.opacity(0.6))
        }
        .padding(.top, 50) // 避开状态栏
        .padding(.leading, 20)
    }

    // MARK: - 下滑关闭手势区域
    private var swipeToCloseGestureArea: some View {
        // 只在屏幕边缘区域响应手势，避免与视频播放器控件冲突
        VStack(spacing: 0) {
            // 顶部手势区域
            Rectangle()
                .fill(Color.clear)
                .frame(height: 120)
                .contentShape(Rectangle())
                .gesture(
                    DragGesture(minimumDistance: 30)
                        .onEnded { value in
                            // 向下滑动退出
                            let verticalDistance = value.translation.height
                            let horizontalDistance = abs(value.translation.width)

                            if verticalDistance > 80 && verticalDistance > horizontalDistance * 1.2 {
                                print("🎬 检测到下滑手势，退出视频播放")
                                onDismiss?()
                            }
                        }
                )

            Spacer()

            // 底部手势区域
            Rectangle()
                .fill(Color.clear)
                .frame(height: 80)
                .contentShape(Rectangle())
                .gesture(
                    DragGesture(minimumDistance: 30)
                        .onEnded { value in
                            // 向下滑动退出
                            let verticalDistance = value.translation.height
                            let horizontalDistance = abs(value.translation.width)

                            if verticalDistance > 60 && verticalDistance > horizontalDistance * 1.2 {
                                print("🎬 检测到下滑手势，退出视频播放")
                                onDismiss?()
                            }
                        }
                )
        }
    }

    // MARK: - 退出按钮
    private var exitButton: some View {
        Button(action: {
            print("🎬 点击退出按钮，退出视频播放")
            onDismiss?()
        }) {
            ZStack {
                // 背景圆圈
                Circle()
                    .fill(.black.opacity(0.5))
                    .frame(width: 40, height: 40)

                // X 图标
                Image(systemName: "xmark")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
        }
        .padding(.top, 50) // 避开状态栏
        .padding(.trailing, 20)
    }

    // MARK: - 方法

    private func loadVideo() {
        videoPlayer.loadVideo(from: mediaFile)
    }

    private func handleStateChange(_ newState: VideoPlayerState) {
        switch newState {
        case .error(let error):
            onError?(error)
        default:
            break
        }
    }
}

// MARK: - 预览
#if DEBUG
struct VideoPlayerView_Previews: PreviewProvider {
    static var previews: some View {
        VideoPlayerView(
            mediaFile: MediaFileInfo(
                id: UUID(),
                name: "示例视频.mp4",
                type: .video,
                fileSize: 1024000,
                creationDate: Date(),
                modificationDate: Date(),
                localURL: URL(fileURLWithPath: "/path/to/video.mp4"),
                thumbnailURL: nil,
                folderPath: "",
                duration: 120.0,
                dimensions: CGSize(width: 1920, height: 1080)
            ),
            mode: .fullScreen
        )
        .preferredColorScheme(.dark)
    }
}
#endif 