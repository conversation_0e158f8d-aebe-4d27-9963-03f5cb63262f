//
//  VideoPlayerView.swift
//  cop
//
//  Created by AI Assistant on 2025/6/1.
//  轻量级视频播放器视图 - 使用iOS原生组件
//

import SwiftUI
import AVFoundation
import AVKit

// MARK: - 轻量级视频播放器视图
struct VideoPlayerView: View {
    let mediaFile: MediaFileInfo
    let mode: VideoPlayerMode

    // 回调
    let onDismiss: (() -> Void)?
    let onError: ((VideoPlayerError) -> Void)?

    // 状态管理 - 使用轻量级播放器
    @StateObject private var videoPlayer: VideoPlayer

    init(
        mediaFile: MediaFileInfo,
        mode: VideoPlayerMode = .fullScreen,
        onDismiss: (() -> Void)? = nil,
        onError: ((VideoPlayerError) -> Void)? = nil
    ) {
        self.mediaFile = mediaFile
        self.mode = mode
        self.onDismiss = onDismiss
        self.onError = onError

        // 为每个视图创建独立的播放器实例
        self._videoPlayer = StateObject(wrappedValue: VideoPlayer(mode: mode))
    }

    var body: some View {
        ZStack {
            // 背景
            Color.black
                .ignoresSafeArea(.all)

            // 主要内容
            mainContent

            // 手势处理层（仅用于退出）
            if mode == .fullScreen {
                gestureOverlay
            }
        }
        .onAppear {
            loadVideo()
        }
        .onDisappear {
            // 清理播放器资源
            videoPlayer.cleanup()
        }
        .onChange(of: videoPlayer.state) { _, newState in
            handleStateChange(newState)
        }
    }

    // MARK: - 主要内容
    @ViewBuilder
    private var mainContent: some View {
        switch videoPlayer.state {
        case .idle:
            EmptyView()

        case .loading:
            loadingView

        case .ready, .playing, .paused:
            if let avPlayer = videoPlayer.getPlayer() {
                playerView(avPlayer)
            }

        case .error(let error):
            errorView(error)
        }
    }

    // MARK: - 加载视图
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))

            if mode == .fullScreen {
                Text("正在加载视频...")
                    .foregroundColor(.white)
                    .font(.headline)
            }
        }
    }

    // MARK: - 播放器视图
    private func playerView(_ avPlayer: AVPlayer) -> some View {
        // 使用iOS原生VideoPlayer，简化实现
        AVKit.VideoPlayer(player: avPlayer)
            .ignoresSafeArea(.all)
            .onAppear {
                print("🎬 VideoPlayer视图出现，播放器状态: \(videoPlayer.state)")
            }
            .onDisappear {
                print("🎬 VideoPlayer视图消失，停止播放")
                avPlayer.pause()
            }
    }

    // MARK: - 错误视图
    private func errorView(_ error: VideoPlayerError) -> some View {
        VStack(spacing: 24) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 64))
                .foregroundColor(.orange)

            Text("播放出现问题")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            Text(error.localizedDescription)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            HStack(spacing: 20) {
                Button("重试") {
                    loadVideo()
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(10)

                Button("返回") {
                    onDismiss?()
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.gray)
                .cornerRadius(10)
            }
            .padding(.top)
        }
        .padding()
    }


    // MARK: - 手势处理层（仅用于退出）
    private var gestureOverlay: some View {
        Color.clear
            .contentShape(Rectangle())
            .gesture(
                DragGesture()
                    .onEnded { value in
                        // 向下滑动退出
                        if value.translation.height > 100 && abs(value.translation.height) > abs(value.translation.width) * 1.5 {
                            onDismiss?()
                        }
                    }
            )
    }

    // MARK: - 方法

    private func loadVideo() {
        videoPlayer.loadVideo(from: mediaFile)
    }

    private func handleStateChange(_ newState: VideoPlayerState) {
        switch newState {
        case .error(let error):
            onError?(error)
        default:
            break
        }
    }
}

// MARK: - 预览
#if DEBUG
struct VideoPlayerView_Previews: PreviewProvider {
    static var previews: some View {
        VideoPlayerView(
            mediaFile: MediaFileInfo(
                id: UUID(),
                name: "示例视频.mp4",
                type: .video,
                fileSize: 1024000,
                creationDate: Date(),
                modificationDate: Date(),
                localURL: URL(fileURLWithPath: "/path/to/video.mp4"),
                thumbnailURL: nil,
                folderPath: "",
                duration: 120.0,
                dimensions: CGSize(width: 1920, height: 1080)
            ),
            mode: .fullScreen
        )
        .preferredColorScheme(.dark)
    }
}
#endif 