//
//  FullScreenMediaViewer.swift
//  cop
//
//  Created by 阿亮 on 2025/6/1.
//

import SwiftUI
import UIKit
import AVFoundation
import AVKit

// MARK: - 全屏媒体浏览器
struct FullScreenMediaViewer: View {
    let mediaFiles: [MediaFileInfo]
    let initialIndex: Int
    @ObservedObject var viewModel: MediaLibraryViewModel
    let initialUIState: UIVisibilityState

    @Environment(\.dismiss) private var dismiss
    @StateObject private var mediaViewerState = MediaViewerState()
    @State private var showingDeleteAlert = false

    init(mediaFiles: [MediaFileInfo], initialIndex: Int, viewModel: MediaLibraryViewModel, initialUIState: UIVisibilityState = .visible) {
        self.mediaFiles = mediaFiles
        self.initialIndex = max(0, min(initialIndex, mediaFiles.count - 1))
        self.viewModel = viewModel
        self.initialUIState = initialUIState
        
        print("🎬 FullScreenMediaViewer 初始化 - 总文件数: \(mediaFiles.count), 初始索引: \(initialIndex)")
        if initialIndex < mediaFiles.count {
            print("🎬 初始文件: \(mediaFiles[initialIndex].name)")
            print("🎬 文件类型: \(mediaFiles[initialIndex].type)")
            print("🎬 文件大小: \(ByteCountFormatter().string(fromByteCount: mediaFiles[initialIndex].fileSize))")
        } else {
            print("❌ 警告: 初始索引超出范围，已调整为: \(self.initialIndex)")
        }
    }

    var currentMediaFile: MediaFileInfo {
        guard mediaViewerState.currentIndex >= 0 && mediaViewerState.currentIndex < mediaFiles.count else {
            print("❌ 当前索引超出范围: \(mediaViewerState.currentIndex), 文件数: \(mediaFiles.count)")
            return MediaFileInfo.placeholder
        }
        return mediaFiles[mediaViewerState.currentIndex]
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 黑色背景
                Color.black
                    .ignoresSafeArea(.all)

                // 主内容区域 - 使用单一视图避免多个播放器实例
                if !mediaFiles.isEmpty && mediaViewerState.currentIndex < mediaFiles.count {
                    let currentMediaFile = mediaFiles[mediaViewerState.currentIndex]

                    MediaContentView(
                        mediaFile: currentMediaFile,
                        uiVisibility: $mediaViewerState.uiVisibility,
                        screenSize: mediaViewerState.screenSize,
                        currentIndex: $mediaViewerState.currentIndex,
                        totalCount: mediaFiles.count,
                        onDismiss: { dismiss() },
                        onToggleUI: { mediaViewerState.toggleUIVisibility() }
                    )
                    .ignoresSafeArea(.all)
                    .id(currentMediaFile.id) // 强制重新创建视图当文件改变时
                    .gesture(
                        // 添加左右滑动手势来切换媒体文件
                        DragGesture()
                            .onEnded { value in
                                let threshold: CGFloat = 50
                                if value.translation.width > threshold && mediaViewerState.currentIndex > 0 {
                                    // 向右滑动，显示上一个
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        mediaViewerState.currentIndex -= 1
                                    }
                                } else if value.translation.width < -threshold && mediaViewerState.currentIndex < mediaFiles.count - 1 {
                                    // 向左滑动，显示下一个
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        mediaViewerState.currentIndex += 1
                                    }
                                }
                            }
                    )
                }

                // UI元素层（标题栏和缩略图栏）
                if mediaViewerState.uiVisibility == .visible {
                    VStack(spacing: 0) {
                        // 标题栏
                        FullScreenNavigationBar(
                            mediaFile: mediaFiles.isEmpty ? nil : currentMediaFile,
                            currentIndex: mediaViewerState.currentIndex,
                            totalCount: mediaFiles.count,
                            onBack: { dismiss() },
                            onDelete: { showingDeleteAlert = true }
                        )

                        Spacer()

                        // 缩略图栏
                        FullScreenThumbnailBar(
                            mediaFiles: mediaFiles,
                            currentIndex: $mediaViewerState.currentIndex
                        )
                    }
                    .transition(.opacity.animation(.easeInOut(duration: 0.15)))
                }
            }
            .onAppear {
                print("🎬 FullScreenMediaViewer 出现 - 设置初始索引: \(initialIndex)")
                // 立即设置正确的索引，避免延迟导致的错误
                mediaViewerState.currentIndex = initialIndex
                mediaViewerState.initialize(initialIndex: initialIndex, initialUIState: initialUIState)
                print("🔄 TabView索引已设置为: \(initialIndex)")
            }
            .onChange(of: geometry.size) { _, newSize in
                mediaViewerState.updateScreenSize(newSize)
            }
            .onReceive(NotificationCenter.default.publisher(for: UIDevice.orientationDidChangeNotification)) { _ in
                mediaViewerState.updateOrientation(UIDevice.current.orientation)
            }
        }
        .statusBarHidden(mediaViewerState.statusBarHidden)
        .animation(.easeInOut(duration: 0.15), value: mediaViewerState.statusBarHidden)
        .ignoresSafeArea(.all)
        .alert("删除文件", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                deleteCurrentMedia()
            }
        } message: {
            Text("确定要删除这个文件吗？此操作无法撤销。")
        }
    }

    // MARK: - 交互处理方法
    private func deleteCurrentMedia() {
        Task {
            await viewModel.deleteMediaFile(currentMediaFile)

            // 删除后的索引调整
            let shouldDismiss = mediaViewerState.adjustIndexAfterDeletion(totalCount: mediaFiles.count)
            if shouldDismiss {
                dismiss()
            }
        }
    }
}

#Preview {
    FullScreenMediaViewer(
        mediaFiles: [],
        initialIndex: 0,
        viewModel: MediaLibraryViewModel(),
        initialUIState: .visible
    )
}